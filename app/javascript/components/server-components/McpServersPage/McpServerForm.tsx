import * as React from "react";
import { useLoaderData, useNavigate, useParams } from "react-router-dom";

import { createMcpServer, updateMcpServer, importMcpServerFromGitHub } from "$app/data/mcp_servers";
import { asyncVoid } from "$app/utils/promise";
import { assertResponseError } from "$app/utils/request";

import { Button } from "$app/components/Button";
import { Icon } from "$app/components/Icons";
import { showAlert } from "$app/components/server-components/Alert";
import { Layout } from "$app/components/server-components/McpServersPage";

type McpServerFormData = {
  name: string;
  description: string;
  endpoint_url: string;
  price_cents: number;
  api_documentation: string;
  supported_tools: string[];
  server_metadata: Record<string, any>;
  github_url?: string;
};

type McpServerFormResponse = {
  mcp_server?: McpServerFormData & { id: string };
  errors?: string[];
};

type CreationMode = "github" | "generate" | "manual";

const McpServerForm = () => {
  const data = useLoaderData() as McpServerFormResponse;
  const navigate = useNavigate();
  const params = useParams();
  const isEditing = !!params.id;

  // Creation mode state - only used for new servers
  const [creationMode, setCreationMode] = React.useState<CreationMode>("github");
  const [githubUrl, setGithubUrl] = React.useState("");
  const [isImporting, setIsImporting] = React.useState(false);

  const [formData, setFormData] = React.useState<McpServerFormData>({
    name: data.mcp_server?.name || "",
    description: data.mcp_server?.description || "",
    endpoint_url: data.mcp_server?.endpoint_url || "",
    price_cents: data.mcp_server?.price_cents || 0,
    api_documentation: data.mcp_server?.api_documentation || "",
    supported_tools: data.mcp_server?.supported_tools || [],
    server_metadata: data.mcp_server?.server_metadata || {},
    github_url: data.mcp_server?.github_url || "",
  });

  const [toolsInput, setToolsInput] = React.useState(formData.supported_tools.join(", "));

  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [errors, setErrors] = React.useState<string[]>([]);

  // Handle GitHub import
  const handleGitHubImport = asyncVoid(async () => {
    if (!githubUrl.trim()) {
      setErrors(["Please enter a GitHub URL"]);
      return;
    }

    setIsImporting(true);
    setErrors([]);

    try {
      const response = await importMcpServerFromGitHub(githubUrl);
      if (response.success && response.mcp_server_data) {
        // Populate form with imported data
        setFormData((prev) => ({
          ...prev,
          ...response.mcp_server_data,
          github_url: githubUrl,
        }));
        setToolsInput(response.mcp_server_data.supported_tools?.join(", ") || "");
        showAlert("MCP server data imported successfully from GitHub!", "success");
      } else {
        setErrors(response.errors || ["Failed to import from GitHub"]);
      }
    } catch (error) {
      assertResponseError(error);
      setErrors([error.message]);
    } finally {
      setIsImporting(false);
    }
  });

  const handleSubmit = asyncVoid(async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrors([]);

    try {
      const submitData = {
        ...formData,
        supported_tools: toolsInput
          .split(",")
          .map((tool) => tool.trim())
          .filter((tool) => tool.length > 0),
      };

      const response = isEditing ? await updateMcpServer(params.id!, submitData) : await createMcpServer(submitData);

      if (response.success) {
        showAlert(isEditing ? "MCP server updated successfully!" : "MCP server created successfully!", "success");
        navigate("/mcp_servers");
      } else {
        setErrors(response.errors || ["An error occurred"]);
      }
    } catch (error) {
      assertResponseError(error);
      setErrors([error.message]);
    } finally {
      setIsSubmitting(false);
    }
  });

  const handleInputChange =
    (field: keyof McpServerFormData) => (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setFormData((prev) => ({
        ...prev,
        [field]: field === "price_cents" ? parseInt(e.target.value) || 0 : e.target.value,
      }));
    };

  return (
    <Layout
      title={isEditing ? "Edit MCP Server" : "Create MCP Server"}
      actions={
        <Button onClick={() => navigate("/mcp_servers")}>
          <Icon name="arrow-left" />
          Back to MCP Servers
        </Button>
      }
    >
      <form onSubmit={handleSubmit} className="form">
        {errors.length > 0 && (
          <div className="alert error">
            <ul>
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Creation Mode Selection - Only show for new servers */}
        {!isEditing && (
          <div className="form-group">
            <label>How would you like to create your MCP server?</label>
            <div className="creation-mode-options">
              <div className={`creation-option ${creationMode === "github" ? "selected" : ""}`}>
                <input
                  type="radio"
                  id="mode-github"
                  name="creation-mode"
                  value="github"
                  checked={creationMode === "github"}
                  onChange={(e) => setCreationMode(e.target.value as CreationMode)}
                />
                <label htmlFor="mode-github" className="creation-option-label">
                  <Icon name="github" />
                  <div>
                    <h3>Import from GitHub</h3>
                    <p>Import an existing MCP server from a GitHub repository</p>
                  </div>
                </label>
              </div>

              <div className={`creation-option ${creationMode === "generate" ? "selected" : ""} disabled`}>
                <input type="radio" id="mode-generate" name="creation-mode" value="generate" disabled />
                <label htmlFor="mode-generate" className="creation-option-label">
                  <Icon name="magic" />
                  <div>
                    <h3>Generate MCP Server</h3>
                    <p>Coming Soon - AI-powered MCP server generation</p>
                    <span className="badge warning">Coming Soon</span>
                  </div>
                </label>
              </div>
            </div>
          </div>
        )}

        {/* GitHub Import Section */}
        {!isEditing && creationMode === "github" && (
          <div className="form-group github-import-section">
            <label htmlFor="github-url">GitHub Repository URL *</label>
            <div className="github-import-input">
              <input
                id="github-url"
                type="url"
                value={githubUrl}
                onChange={(e) => setGithubUrl(e.target.value)}
                placeholder="https://github.com/username/mcp-server-repo"
                required
              />
              <Button
                type="button"
                onClick={handleGitHubImport}
                disabled={isImporting || !githubUrl.trim()}
                color="accent"
              >
                {isImporting ? (
                  <>
                    <Icon name="loading" />
                    Importing...
                  </>
                ) : (
                  <>
                    <Icon name="download" />
                    Import
                  </>
                )}
              </Button>
            </div>
            <small>Enter the GitHub URL of your MCP server repository to automatically populate the form</small>
          </div>
        )}

        <div className="form-group">
          <label htmlFor="name">Server Name *</label>
          <input
            id="name"
            type="text"
            value={formData.name}
            onChange={handleInputChange("name")}
            placeholder="My Awesome MCP Server"
            required
          />
        </div>

        <div className="form-group">
          <label htmlFor="description">Description</label>
          <textarea
            id="description"
            value={formData.description}
            onChange={handleInputChange("description")}
            placeholder="Describe what your MCP server does..."
            rows={4}
          />
        </div>

        <div className="form-group">
          <label htmlFor="endpoint_url">Endpoint URL *</label>
          <input
            id="endpoint_url"
            type="url"
            value={formData.endpoint_url}
            onChange={handleInputChange("endpoint_url")}
            placeholder="https://api.example.com/mcp"
            required
          />
          <small>The URL where your MCP server is hosted</small>
        </div>

        <div className="form-group">
          <label htmlFor="price_cents">Price (in cents)</label>
          <input
            id="price_cents"
            type="number"
            value={formData.price_cents}
            onChange={handleInputChange("price_cents")}
            placeholder="0"
            min="0"
          />
          <small>Price in cents (e.g., 500 = $5.00). Set to 0 for free.</small>
        </div>

        <div className="form-group">
          <label htmlFor="supported_tools">Supported Tools</label>
          <input
            id="supported_tools"
            type="text"
            value={toolsInput}
            onChange={(e) => setToolsInput(e.target.value)}
            placeholder="search, analyze, generate, translate"
          />
          <small>Comma-separated list of tools your server supports</small>
        </div>

        <div className="form-group">
          <label htmlFor="api_documentation">API Documentation</label>
          <textarea
            id="api_documentation"
            value={formData.api_documentation}
            onChange={handleInputChange("api_documentation")}
            placeholder="Provide documentation for your MCP server API..."
            rows={6}
          />
        </div>

        {/* GitHub URL field - show for editing or if imported from GitHub */}
        {(isEditing || formData.github_url) && (
          <div className="form-group">
            <label htmlFor="github_url">GitHub Repository URL</label>
            <input
              id="github_url"
              type="url"
              value={formData.github_url || ""}
              onChange={handleInputChange("github_url")}
              placeholder="https://github.com/username/mcp-server-repo"
              readOnly={!isEditing}
            />
            <small>The GitHub repository where this MCP server is hosted</small>
          </div>
        )}

        <div className="form-actions">
          <Button type="submit" color="accent" disabled={isSubmitting}>
            {isSubmitting ? "Saving..." : isEditing ? "Update Server" : "Create Server"}
          </Button>
          <Button type="button" onClick={() => navigate("/mcp_servers")}>
            Cancel
          </Button>
        </div>
      </form>
    </Layout>
  );
};

export default McpServerForm;
